<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'mra' => [
        'base_url' => env('MRA_API_BASE_URL', 'https://eservices.mra.mw/ws/IATACoreAPI'),
        'client_id' => env('MRA_API_CLIENT_ID', 'aircargo'),
        'client_secret' => env('MRA_API_CLIENT_SECRET', 'IPass2025!'),
        'notifications_enabled' => env('MRA_API_NOTIFICATIONS_ENABLED', false),
        'notification_recipients' => explode(',', env('MRA_API_NOTIFICATION_RECIPIENTS', '')),
        'use_soap_client' => env('MRA_USE_SOAP_CLIENT', false),
        'soap_client_url' => env('MRA_SOAP_CLIENT_URL', 'http://localhost:8004'),
    ],

];
