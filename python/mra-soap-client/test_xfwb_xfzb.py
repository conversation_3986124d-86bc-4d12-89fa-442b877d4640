#!/usr/bin/env python3
"""
Test script for XFWB and XFZB MRA SOAP submissions.

This script tests the enhanced MRA SOAP client with XFWB and XFZB support.
"""

import json
import logging
import os
import sys
from datetime import datetime, timezone

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_operations import DatabaseManager
from mra_soap_client import MRASOAPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_xfwb_submission():
    """Test XFWB (Master Waybill) submission."""
    logger.info("=" * 60)
    logger.info("TESTING XFWB (Master Waybill) SUBMISSION")
    logger.info("=" * 60)

    # Initialize clients in file mode for testing
    soap_client = MRASOAPClient(file_mode=True)
    db_manager = DatabaseManager()

    if not db_manager.connect():
        logger.error("Failed to connect to database")
        return False

    try:
        # Get master waybills with XML data
        master_waybills = db_manager.get_master_waybills(limit=1)

        if not master_waybills:
            logger.warning("No master waybills with XML data found")
            return False

        waybill = master_waybills[0]
        logger.info(
            f"Testing with master waybill: {waybill.get('awb_number', 'Unknown')}"
        )

        # Extract XML data
        xml_data = waybill.get("xml_data")
        if not xml_data:
            logger.error("No XML data found in master waybill")
            return False

        # Submit to MRA
        result = soap_client.send_xml_to_mra(xml_data, "MAWB", "AC")

        logger.info("XFWB Submission Result:")
        logger.info(f"  Success: {result.get('success', False)}")
        logger.info(f"  Message: {result.get('message', 'No message')}")

        if result.get("success"):
            logger.info(f"  XML File: {result.get('xml_file', 'N/A')}")
            logger.info(f"  SOAP File: {result.get('soap_file', 'N/A')}")
            logger.info(f"  XFNM Content Available: {bool(result.get('xfnm_content'))}")
        else:
            logger.error(f"  Error: {result.get('error', 'Unknown error')}")

        return result.get("success", False)

    except Exception as e:
        logger.error(f"Error testing XFWB submission: {e}")
        return False


def test_xfzb_submission():
    """Test XFZB (House Waybill) submission."""
    logger.info("=" * 60)
    logger.info("TESTING XFZB (House Waybill) SUBMISSION")
    logger.info("=" * 60)

    # Initialize clients in file mode for testing
    soap_client = MRASOAPClient(file_mode=True)
    db_manager = DatabaseManager()

    if not db_manager.connect():
        logger.error("Failed to connect to database")
        return False

    try:
        # Get house waybills with XML data
        house_waybills = db_manager.get_house_waybills(limit=1)

        if not house_waybills:
            logger.warning("No house waybills with XML data found")
            return False

        waybill = house_waybills[0]
        logger.info(
            f"Testing with house waybill: {waybill.get('hawb_number', 'Unknown')}"
        )

        # Extract XML data
        xml_data = waybill.get("xml_data")
        if not xml_data:
            logger.error("No XML data found in house waybill")
            return False

        # Submit to MRA
        result = soap_client.send_xml_to_mra(xml_data, "XFZB", "AC")

        logger.info("XFZB Submission Result:")
        logger.info(f"  Success: {result.get('success', False)}")
        logger.info(f"  Message: {result.get('message', 'No message')}")

        if result.get("success"):
            logger.info(f"  XML File: {result.get('xml_file', 'N/A')}")
            logger.info(f"  SOAP File: {result.get('soap_file', 'N/A')}")
            logger.info(f"  XFNM Content Available: {bool(result.get('xfnm_content'))}")
        else:
            logger.error(f"  Error: {result.get('error', 'Unknown error')}")

        return result.get("success", False)

    except Exception as e:
        logger.error(f"Error testing XFZB submission: {e}")
        return False


def test_airline_code_extraction():
    """Test airline code extraction from XML data."""
    logger.info("=" * 60)
    logger.info("TESTING AIRLINE CODE EXTRACTION")
    logger.info("=" * 60)

    soap_client = MRASOAPClient(file_mode=True)

    # Test with sample XML data
    test_cases = [
        {
            "name": "Flight Number Format",
            "xml": '<?xml version="1.0"?><FlightManifest><FlightNumber>KQ775</FlightNumber></FlightManifest>',
            "message_type": "XFFM",
            "expected": "KQ",
        },
        {
            "name": "AWB Number Format",
            "xml": '<?xml version="1.0"?><Waybill><AWBNumber>706-12345678</AWBNumber></Waybill>',
            "message_type": "XFWB",
            "expected": "AC",
        },
        {
            "name": "Carrier Party Format",
            "xml": '<?xml version="1.0"?><Waybill><CarrierParty><PrimaryID>ET</PrimaryID></CarrierParty></Waybill>',
            "message_type": "XFWB",
            "expected": "ET",
        },
        {
            "name": "XFZB UsedLogisticsTransportMeans - Direct Airline Code",
            "xml": '<?xml version="1.0"?><HouseWaybill xmlns:ram="iata:datamodel:3"><ram:UsedLogisticsTransportMeans><ram:Name>KQ</ram:Name></ram:UsedLogisticsTransportMeans></HouseWaybill>',
            "message_type": "XFZB",
            "expected": "KQ",
        },
        {
            "name": "XFZB UsedLogisticsTransportMeans - Flight Number",
            "xml": '<?xml version="1.0"?><HouseWaybill xmlns:ram="iata:datamodel:3"><ram:UsedLogisticsTransportMeans><ram:Name>ET304</ram:Name></ram:UsedLogisticsTransportMeans></HouseWaybill>',
            "message_type": "XFZB",
            "expected": "ET",
        },
        {
            "name": "XFZB No Valid Airline Code",
            "xml": '<?xml version="1.0"?><HouseWaybill><InvalidElement>NoAirlineCode</InvalidElement></HouseWaybill>',
            "message_type": "XFZB",
            "expected": None,
        },
    ]

    for test_case in test_cases:
        logger.info(f"Testing: {test_case['name']}")
        extracted = soap_client._extract_airline_code_from_xml(
            test_case["xml"], test_case["message_type"]
        )
        logger.info(f"  Expected: {test_case['expected']}, Got: {extracted}")

        if extracted == test_case["expected"]:
            logger.info("  ✓ PASS")
        else:
            logger.warning("  ✗ FAIL")


def main():
    """Main test function."""
    logger.info("Starting XFWB and XFZB MRA SOAP Client Tests")
    logger.info("=" * 80)

    # Test airline code extraction
    test_airline_code_extraction()

    # Test XFWB submission
    xfwb_success = test_xfwb_submission()

    # Test XFZB submission
    xfzb_success = test_xfzb_submission()

    # Summary
    logger.info("=" * 80)
    logger.info("TEST SUMMARY")
    logger.info("=" * 80)
    logger.info(f"XFWB (Master Waybill) Test: {'PASS' if xfwb_success else 'FAIL'}")
    logger.info(f"XFZB (House Waybill) Test: {'PASS' if xfzb_success else 'FAIL'}")

    if xfwb_success and xfzb_success:
        logger.info(
            "🎉 All tests passed! XFWB and XFZB functionality is working correctly."
        )
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    exit(main())
