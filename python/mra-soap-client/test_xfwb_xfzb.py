#!/usr/bin/env python3
"""
Test script for XFWB and XFZB MRA SOAP submissions.

This script tests the enhanced MRA SOAP client with XFWB and XFZB support.
"""

import json
import logging
import os
import sys
from datetime import datetime, timezone

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_operations import DatabaseManager
from mra_soap_client import MRASOAPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_xfwb_submission():
    """Test XFWB (Master Waybill) submission."""
    logger.info("=" * 60)
    logger.info("TESTING XFWB (Master Waybill) SUBMISSION")
    logger.info("=" * 60)

    # Initialize clients in file mode for testing
    soap_client = MRASOAPClient(file_mode=True)
    db_manager = DatabaseManager()

    if not db_manager.connect():
        logger.error("Failed to connect to database")
        return False

    try:
        # Get master waybills with XML data
        master_waybills = db_manager.get_master_waybills(limit=1)

        if not master_waybills:
            logger.warning("No master waybills with XML data found")
            return False

        waybill = master_waybills[0]
        logger.info(
            f"Testing with master waybill: {waybill.get('awb_number', 'Unknown')}"
        )

        # Extract XML data
        xml_data = waybill.get("xml_data")
        if not xml_data:
            logger.error("No XML data found in master waybill")
            return False

        # Submit to MRA
        result = soap_client.send_xml_to_mra(xml_data, "MAWB", "AC")

        logger.info("XFWB Submission Result:")
        logger.info(f"  Success: {result.get('success', False)}")
        logger.info(f"  Message: {result.get('message', 'No message')}")

        if result.get("success"):
            logger.info(f"  XML File: {result.get('xml_file', 'N/A')}")
            logger.info(f"  SOAP File: {result.get('soap_file', 'N/A')}")
            logger.info(f"  XFNM Content Available: {bool(result.get('xfnm_content'))}")
        else:
            logger.error(f"  Error: {result.get('error', 'Unknown error')}")

        return result.get("success", False)

    except Exception as e:
        logger.error(f"Error testing XFWB submission: {e}")
        return False


def test_xfzb_submission():
    """Test XFZB (House Waybill) submission with detailed envelope examination."""
    logger.info("=" * 60)
    logger.info("TESTING XFZB (House Waybill) SUBMISSION")
    logger.info("=" * 60)

    # Initialize clients in file mode for testing
    soap_client = MRASOAPClient(file_mode=True)
    db_manager = DatabaseManager()

    if not db_manager.connect():
        logger.error("Failed to connect to database")
        return False

    try:
        # Get house waybills with XML data
        house_waybills = db_manager.get_house_waybills(limit=5)

        if not house_waybills:
            logger.warning("No house waybills with XML data found")
            return False

        for i, waybill in enumerate(house_waybills):
            logger.info(f"\n--- Testing House Waybill {i+1} ---")
            logger.info(f"HAWB Number: {waybill.get('hawb_number', 'Unknown')}")
            logger.info(f"MAWB Number: {waybill.get('mawb_number', 'Unknown')}")

            # Extract XML data
            xml_data = waybill.get("xml_data")
            if not xml_data:
                logger.warning("No XML data found in this house waybill, skipping...")
                continue

            # First, test airline code extraction
            logger.info("Testing airline code extraction...")
            extracted_airline = soap_client._extract_airline_code_from_xml(
                xml_data, "XFZB"
            )
            logger.info(f"Extracted airline code: {extracted_airline}")

            if extracted_airline is None:
                logger.warning(
                    "❌ Airline extraction failed - submission would be aborted"
                )
                logger.info("Let's examine the XML structure...")

                # Extract actual XML for examination
                actual_xml = xml_data
                if isinstance(xml_data, dict) and "xml" in xml_data:
                    actual_xml = xml_data["xml"]
                elif isinstance(xml_data, str):
                    try:
                        import json

                        parsed = json.loads(xml_data)
                        if isinstance(parsed, dict) and "xml" in parsed:
                            actual_xml = parsed["xml"]
                    except:
                        pass

                # Show first 1000 characters of XML for analysis
                logger.info("XML Structure (first 1000 chars):")
                logger.info(
                    actual_xml[:1000] + "..."
                    if len(str(actual_xml)) > 1000
                    else str(actual_xml)
                )
                continue

            # Submit to MRA with extracted airline code
            logger.info(f"Submitting with airline code: {extracted_airline}")
            result = soap_client.send_xml_to_mra(xml_data, "XFZB", extracted_airline)

            logger.info("XFZB Submission Result:")
            logger.info(f"  Success: {result.get('success', False)}")
            logger.info(f"  Message: {result.get('message', 'No message')}")

            if result.get("success"):
                logger.info(f"  XML File: {result.get('xml_file', 'N/A')}")
                logger.info(f"  SOAP File: {result.get('soap_file', 'N/A')}")
                logger.info(
                    f"  XFNM Content Available: {bool(result.get('xfnm_content'))}"
                )

                # Examine the SOAP envelope
                soap_file = result.get("soap_file")
                if soap_file:
                    logger.info("\n--- SOAP ENVELOPE EXAMINATION ---")
                    try:
                        with open(soap_file, "r") as f:
                            soap_content = f.read()

                        # Extract airline code from SOAP envelope
                        import re

                        airline_match = re.search(
                            r"<api3:airline>([^<]+)</api3:airline>", soap_content
                        )
                        if airline_match:
                            soap_airline = airline_match.group(1)
                            logger.info(
                                f"✅ Airline code in SOAP envelope: {soap_airline}"
                            )
                        else:
                            logger.error(
                                "❌ Could not find airline code in SOAP envelope"
                            )

                        # Show relevant parts of SOAP envelope
                        logger.info("SOAP Envelope Structure:")
                        lines = soap_content.split("\n")
                        for line_num, line in enumerate(
                            lines[:20], 1
                        ):  # First 20 lines
                            if (
                                "airline" in line
                                or "sendMessageXFZB" in line
                                or "xfzb" in line
                            ):
                                logger.info(f"  Line {line_num}: {line.strip()}")

                    except Exception as e:
                        logger.error(f"Error reading SOAP file: {e}")

                return True
            else:
                logger.error(f"  Error: {result.get('error', 'Unknown error')}")
                logger.error(f"  Error Code: {result.get('error_code', 'Unknown')}")

        return False

    except Exception as e:
        logger.error(f"Error testing XFZB submission: {e}")
        return False


def test_airline_code_extraction():
    """Test airline code extraction from XML data."""
    logger.info("=" * 60)
    logger.info("TESTING AIRLINE CODE EXTRACTION")
    logger.info("=" * 60)

    soap_client = MRASOAPClient(file_mode=True)

    # Test with sample XML data
    test_cases = [
        {
            "name": "Flight Number Format",
            "xml": '<?xml version="1.0"?><FlightManifest><FlightNumber>KQ775</FlightNumber></FlightManifest>',
            "message_type": "XFFM",
            "expected": "KQ",
        },
        {
            "name": "AWB Number Format",
            "xml": '<?xml version="1.0"?><Waybill><AWBNumber>706-12345678</AWBNumber></Waybill>',
            "message_type": "XFWB",
            "expected": "AC",
        },
        {
            "name": "Carrier Party Format",
            "xml": '<?xml version="1.0"?><Waybill><CarrierParty><PrimaryID>ET</PrimaryID></CarrierParty></Waybill>',
            "message_type": "XFWB",
            "expected": "ET",
        },
        {
            "name": "XFZB UsedLogisticsTransportMeans - Direct Airline Code",
            "xml": '<?xml version="1.0"?><HouseWaybill xmlns:ram="iata:datamodel:3"><ram:UsedLogisticsTransportMeans><ram:Name>KQ</ram:Name></ram:UsedLogisticsTransportMeans></HouseWaybill>',
            "message_type": "XFZB",
            "expected": "KQ",
        },
        {
            "name": "XFZB UsedLogisticsTransportMeans - Flight Number",
            "xml": '<?xml version="1.0"?><HouseWaybill xmlns:ram="iata:datamodel:3"><ram:UsedLogisticsTransportMeans><ram:Name>ET304</ram:Name></ram:UsedLogisticsTransportMeans></HouseWaybill>',
            "message_type": "XFZB",
            "expected": "ET",
        },
        {
            "name": "XFZB No Valid Airline Code",
            "xml": '<?xml version="1.0"?><HouseWaybill><InvalidElement>NoAirlineCode</InvalidElement></HouseWaybill>',
            "message_type": "XFZB",
            "expected": None,
        },
    ]

    for test_case in test_cases:
        logger.info(f"Testing: {test_case['name']}")
        extracted = soap_client._extract_airline_code_from_xml(
            test_case["xml"], test_case["message_type"]
        )
        logger.info(f"  Expected: {test_case['expected']}, Got: {extracted}")

        if extracted == test_case["expected"]:
            logger.info("  ✓ PASS")
        else:
            logger.warning("  ✗ FAIL")


def main():
    """Main test function."""
    logger.info("Starting XFWB and XFZB MRA SOAP Client Tests")
    logger.info("=" * 80)

    # Test airline code extraction
    test_airline_code_extraction()

    # Test XFWB submission
    xfwb_success = test_xfwb_submission()

    # Test XFZB submission
    xfzb_success = test_xfzb_submission()

    # Summary
    logger.info("=" * 80)
    logger.info("TEST SUMMARY")
    logger.info("=" * 80)
    logger.info(f"XFWB (Master Waybill) Test: {'PASS' if xfwb_success else 'FAIL'}")
    logger.info(f"XFZB (House Waybill) Test: {'PASS' if xfzb_success else 'FAIL'}")

    if xfwb_success and xfzb_success:
        logger.info(
            "🎉 All tests passed! XFWB and XFZB functionality is working correctly."
        )
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    exit(main())
