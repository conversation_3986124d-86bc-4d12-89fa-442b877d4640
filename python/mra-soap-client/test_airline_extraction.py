#!/usr/bin/env python3
"""
Test script to verify the airline code extraction fix for XFWB messages.
"""

import json
import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mra_soap_client import MRASOAPClient


def test_xfwb_airline_extraction():
    """Test XFWB airline code extraction with sample XML data."""

    # Sample XFWB XML with Kenya Airways flight numbers
    sample_xfwb_xml = """<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:ccts="urn:un:unece:uncefact:documentation:standard:CoreComponentsTechnicalSpecification:2"
             xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:8"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:ibws="http://information-broker.int.kn/WebService"
             xmlns:rsm="iata:waybill:1"
             xmlns:ram="iata:datamodel:3"
             xsi:schemaLocation="iata:waybill:1 Waybill_1.xsd">
   <rsm:MessageHeaderDocument>
      <ram:ID>706-22334455</ram:ID>
      <ram:Name>Air Waybill</ram:Name>
      <ram:TypeCode>740</ram:TypeCode>
   </rsm:MessageHeaderDocument>
   <rsm:BusinessHeaderDocument>
      <ram:ID>706-22334455</ram:ID>
   </rsm:BusinessHeaderDocument>
   <rsm:Waybill>
      <ram:ID>706-22334455</ram:ID>
      <ram:IncludedLogisticsTransportMovement>
         <ram:ID>KQ703</ram:ID>
         <ram:ModeCode>4</ram:ModeCode>
      </ram:IncludedLogisticsTransportMovement>
      <ram:IncludedLogisticsTransportMovement>
         <ram:ID>KQ304</ram:ID>
         <ram:ModeCode>4</ram:ModeCode>
      </ram:IncludedLogisticsTransportMovement>
   </rsm:Waybill>
</rsm:Waybill>"""

    # Test with JSON-wrapped format (as stored in database)
    json_wrapped_xml = {"xml": sample_xfwb_xml}

    print("Testing XFWB Airline Code Extraction")
    print("=" * 50)

    # Initialize MRA SOAP client
    client = MRASOAPClient()

    # Test 1: Direct XML string
    print("\nTest 1: Direct XML string")
    airline_code = client._extract_airline_code_from_xml(sample_xfwb_xml, "XFWB")
    print(f"Extracted airline code: {airline_code}")
    print(f"Expected: KQ (from flight numbers KQ703, KQ304)")
    print(f"Result: {'✅ PASS' if airline_code == 'KQ' else '❌ FAIL'}")

    # Test 2: JSON-wrapped XML (database format)
    print("\nTest 2: JSON-wrapped XML (database format)")
    airline_code = client._extract_airline_code_from_xml(
        json.dumps(json_wrapped_xml), "XFWB"
    )
    print(f"Extracted airline code: {airline_code}")
    print(f"Expected: KQ (from flight numbers KQ703, KQ304)")
    print(f"Result: {'✅ PASS' if airline_code == 'KQ' else '❌ FAIL'}")

    # Test 3: Dictionary format
    print("\nTest 3: Dictionary format")
    airline_code = client._extract_airline_code_from_xml(json_wrapped_xml, "XFWB")
    print(f"Extracted airline code: {airline_code}")
    print(f"Expected: KQ (from flight numbers KQ703, KQ304)")
    print(f"Result: {'✅ PASS' if airline_code == 'KQ' else '❌ FAIL'}")

    # Test 4: XFWB without flight numbers (should fall back to AWB)
    print("\nTest 4: XFWB without flight numbers (AWB fallback)")
    xfwb_no_flights = """<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3">
   <rsm:MessageHeaderDocument>
      <ram:ID>706-22334455</ram:ID>
   </rsm:MessageHeaderDocument>
   <rsm:Waybill>
      <ram:ID>706-22334455</ram:ID>
   </rsm:Waybill>
</rsm:Waybill>"""
    airline_code = client._extract_airline_code_from_xml(xfwb_no_flights, "XFWB")
    print(f"Extracted airline code: {airline_code}")
    print(f"Expected: AC (from AWB 706-22334455 fallback)")
    print(f"Result: {'✅ PASS' if airline_code == 'AC' else '❌ FAIL'}")

    # Test 5: Non-XFWB message (should use generic extraction)
    print("\nTest 5: Non-XFWB message (generic extraction)")
    airline_code = client._extract_airline_code_from_xml(sample_xfwb_xml, "XFFM")
    print(f"Extracted airline code: {airline_code}")
    print(f"Expected: KQ (from flight numbers in generic extraction)")
    print(
        f"Result: {'✅ PASS' if airline_code == 'KQ' else '❌ FAIL (generic extraction uses different patterns)'}"
    )

    print("\n" + "=" * 50)
    print("Test Summary:")
    print("- XFWB messages now prioritize flight number extraction")
    print("- Flight numbers like 'KQ703' extract airline code 'KQ'")
    print("- Falls back to AWB extraction if no flight numbers found")
    print("- Non-XFWB messages use generic extraction logic")


if __name__ == "__main__":
    test_xfwb_airline_extraction()
