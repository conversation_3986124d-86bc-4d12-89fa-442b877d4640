#!/usr/bin/env python3
"""
Test script to verify the XFWB airline code extraction fix with real MRA submission.
"""

import json
import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mra_soap_client import MRASOAPClient


def test_real_xfwb_submission():
    """Test XFWB submission with the fixed airline code extraction."""

    # Real XFWB XML with Kenya Airways flight numbers (based on the sample we saw)
    real_xfwb_xml = """<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:ccts="urn:un:unece:uncefact:documentation:standard:CoreComponentsTechnicalSpecification:2"
             xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:8"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:ibws="http://information-broker.int.kn/WebService"
             xmlns:rsm="iata:waybill:1"
             xmlns:ram="iata:datamodel:3"
             xsi:schemaLocation="iata:waybill:1 Waybill_1.xsd">
   <rsm:MessageHeaderDocument>
      <ram:ID>706-22334455</ram:ID>
      <ram:Name>Air Waybill</ram:Name>
      <ram:TypeCode>740</ram:TypeCode>
      <ram:IssueDateTime>2014-01-27T14:53:23</ram:IssueDateTime>
      <ram:PurposeCode>Creation</ram:PurposeCode>
      <ram:VersionID>4.00</ram:VersionID>
      <ram:ConversationID>OSAG21180009</ram:ConversationID>
   </rsm:MessageHeaderDocument>
   <rsm:BusinessHeaderDocument>
      <ram:ID>706-22334455</ram:ID>
      <ram:SenderAssignedID>422021744010440</ram:SenderAssignedID>
   </rsm:BusinessHeaderDocument>
   <rsm:Waybill>
      <ram:ID>706-22334455</ram:ID>
      <ram:IncludedLogisticsTransportMovement>
         <ram:ID>KQ703</ram:ID>
         <ram:ModeCode>4</ram:ModeCode>
         <ram:DepartureEvent>
            <ram:OccurrenceDateTime>2014-01-28T00:00:00</ram:OccurrenceDateTime>
            <ram:OccurrenceLogisticsLocation>
               <ram:ID>GRU</ram:ID>
            </ram:OccurrenceLogisticsLocation>
         </ram:DepartureEvent>
         <ram:ArrivalEvent>
            <ram:OccurrenceDateTime>2014-01-28T00:00:00</ram:OccurrenceDateTime>
            <ram:OccurrenceLogisticsLocation>
               <ram:ID>LLW</ram:ID>
            </ram:OccurrenceLogisticsLocation>
         </ram:ArrivalEvent>
      </ram:IncludedLogisticsTransportMovement>
      <ram:IncludedLogisticsTransportMovement>
         <ram:ID>KQ304</ram:ID>
         <ram:ModeCode>4</ram:ModeCode>
         <ram:DepartureEvent>
            <ram:OccurrenceDateTime>2014-01-28T00:00:00</ram:OccurrenceDateTime>
            <ram:OccurrenceLogisticsLocation>
               <ram:ID>HHN</ram:ID>
            </ram:OccurrenceLogisticsLocation>
         </ram:DepartureEvent>
         <ram:ArrivalEvent>
            <ram:OccurrenceDateTime>2014-01-28T00:00:00</ram:OccurrenceDateTime>
            <ram:OccurrenceLogisticsLocation>
               <ram:ID>FRA</ram:ID>
            </ram:OccurrenceLogisticsLocation>
         </ram:ArrivalEvent>
      </ram:IncludedLogisticsTransportMovement>
   </rsm:Waybill>
</rsm:Waybill>"""

    # Test with JSON-wrapped format (as stored in database)
    json_wrapped_xml = {"xml": real_xfwb_xml}

    print("Testing Real XFWB MRA Submission")
    print("=" * 50)

    # Initialize MRA SOAP client
    client = MRASOAPClient()

    # Test airline code extraction
    print("\n1. Testing Airline Code Extraction:")
    airline_code = client._extract_airline_code_from_xml(
        json.dumps(json_wrapped_xml), "XFWB"
    )
    print(f"   Extracted airline code: {airline_code}")
    print(f"   Expected: KQ (Kenya Airways)")
    print(f"   Result: {'✅ PASS' if airline_code == 'KQ' else '❌ FAIL'}")

    if airline_code != "KQ":
        print("   ❌ CRITICAL: Airline code extraction failed!")
        return False

    # Test SOAP envelope generation
    print("\n2. Testing SOAP Envelope Generation:")
    try:
        soap_envelope = client._create_soap_envelope(
            "sendMessageXFWB", real_xfwb_xml, airline_code
        )
        print("   ✅ SOAP envelope generated successfully")

        # Check if the correct airline code is in the envelope
        if f"<api3:airline>{airline_code}</api3:airline>" in soap_envelope:
            print(f"   ✅ Correct airline code '{airline_code}' found in SOAP envelope")
        else:
            print(f"   ❌ Airline code '{airline_code}' not found in SOAP envelope")
            return False

    except Exception as e:
        print(f"   ❌ SOAP envelope generation failed: {e}")
        return False

    # Test MRA submission (in file mode to avoid actual submission)
    print("\n3. Testing MRA Submission (File Mode):")
    try:
        # Ensure we're in file mode
        os.environ["MRA_FILE_MODE"] = "true"

        result = client.send_xml_to_mra(
            json.dumps(json_wrapped_xml), "MAWB", airline_code
        )

        if result.get("success"):
            print("   ✅ XFWB submission successful (file mode)")
            print(f"   📁 XML file: {result.get('xml_file', 'N/A')}")
            print(f"   📁 SOAP file: {result.get('soap_file', 'N/A')}")

            # Verify the files contain the correct airline code
            if result.get("xml_file") and os.path.exists(result["xml_file"]):
                filename = os.path.basename(result["xml_file"])
                if filename.startswith(f"{airline_code}_"):
                    print(
                        f"   ✅ File named correctly with airline code '{airline_code}'"
                    )
                else:
                    print(
                        f"   ❌ File not named with correct airline code. Expected '{airline_code}', got: {filename}"
                    )
                    return False
        else:
            print(
                f"   ❌ XFWB submission failed: {result.get('error', 'Unknown error')}"
            )
            return False

    except Exception as e:
        print(f"   ❌ MRA submission test failed: {e}")
        return False

    print("\n" + "=" * 50)
    print("✅ ALL TESTS PASSED!")
    print("\nSummary of Fix:")
    print("- XFWB messages now extract airline code from flight numbers (KQ703 → KQ)")
    print("- Previously extracted from AWB number (706-22334455 → AC)")
    print("- MRA submissions will now use correct airline code 'KQ' for Kenya Airways")
    print("- This should resolve the MRA authorization errors")

    return True


if __name__ == "__main__":
    success = test_real_xfwb_submission()
    if success:
        print("\n🎉 XFWB MRA API airline code extraction fix verified!")
    else:
        print("\n❌ Fix verification failed!")
        sys.exit(1)
