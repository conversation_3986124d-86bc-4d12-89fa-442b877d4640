#!/usr/bin/env python3
"""
XFWB database operations.

This module provides functionality for saving XFWB data to the database.
"""

import os
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.base_operations import BaseDatabaseOperations


class XFWBDatabaseOperations(BaseDatabaseOperations):
    """
    Database operations for XFWB (Master Air Waybill) data.

    This class handles saving XFWB data to the database including
    master waybills, parties, and related information.
    """

    def __init__(self, db_connection, db_cursor, user_id=1, branch_id=1, logger=None):
        """
        Initialize XFWB database operations.

        Args:
            db_connection: Database connection object.
            db_cursor: Database cursor object.
            user_id (int): User ID for audit fields.
            branch_id (int): Branch ID for audit fields.
            logger (logging.Logger): Logger instance.
        """
        super().__init__(db_connection, db_cursor, logger)
        self.user_id = user_id
        self.branch_id = branch_id

    def save_data(self, data):
        """
        Save XFWB data to the database.

        Args:
            data (dict): Extracted and validated XFWB data.

        Returns:
            dict: Result of save operation including IDs and status.
        """
        result = {"success": False, "awb_id": None, "errors": [], "warnings": []}

        try:
            # Import partial detection service
            import os
            import sys

            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from services.partial_detection_service import PartialDetectionService

            # Initialize partial detection service
            partial_service = PartialDetectionService(self, self.logger)

            # Check for partial AWB detection
            partial_result = partial_service.detect_partial_from_totals_comparison(
                awb_number=data["awb_number"],
                incoming_pieces=data.get("total_pieces", 0),
                incoming_weight=data.get("total_weight", 0.0),
                source="XFWB",
                manifest_id=data.get("manifest_id"),
                is_house=False,
            )

            # Handle partial detection results
            if partial_result["action"] == "FLAG_REVIEW":
                result["errors"].extend(partial_result["errors"])
                result["warnings"].append(
                    f"AWB {data['awb_number']} flagged for manual review due to totals discrepancy"
                )
                return result
            elif partial_result["action"] == "CREATE_PARTIAL":
                result["warnings"].append(
                    f"Created partial waybill {partial_result['partial_id']} for originally received portion"
                )
                # Continue with saving the updated AWB with new totals
            elif partial_result["action"] == "XFWB_CORRECTION":
                result["warnings"].append(
                    f"XFWB correction applied: Created partial waybill {partial_result['partial_id']} for remaining portion"
                )
                # Continue with saving the updated AWB with XFWB totals

            # Check if AWB already exists (after partial detection)
            existing_awb = self.find_existing_awb(data["awb_number"])

            # Handle existing AWB based on partial detection results
            if existing_awb:
                awb_id = existing_awb[0]  # Get existing AWB ID

                if partial_result["action"] in ["CREATE_PARTIAL", "XFWB_CORRECTION"]:
                    # Partial detection handled the update
                    if partial_result["totals_updated"]:
                        result["awb_id"] = awb_id
                    else:
                        result["errors"].append(
                            "Failed to update AWB totals during partial processing"
                        )
                        return result
                elif partial_result["action"] == "NONE":
                    # No partial detected, update existing AWB with new XFWB data
                    self.logger.info(
                        f"Updating existing AWB {data['awb_number']} with XFWB data"
                    )
                    success = self.update_master_waybill(awb_id, data)
                    if success:
                        result["awb_id"] = awb_id
                    else:
                        result["errors"].append(
                            f"Failed to update existing AWB {data['awb_number']}"
                        )
                        return result
                else:
                    # Other actions like FLAG_REVIEW
                    result["errors"].append(
                        f"AWB {data['awb_number']} requires manual review"
                    )
                    return result
            else:
                # Save new master waybill
                awb_id = self.save_master_waybill(data)
                result["awb_id"] = awb_id

            # Save party information
            self.save_party_information(awb_id, data)

            # Save cargo details
            self.save_cargo_details(awb_id, data)

            # Commit transaction
            self.commit_transaction()

            result["success"] = True
            self.logger.info(
                f"Successfully saved XFWB data for AWB {data['awb_number']}"
            )

        except Exception as e:
            # Rollback transaction on error
            self.rollback_transaction()
            error_msg = f"Error saving XFWB data: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)

        return result

    def find_existing_awb(self, awb_number):
        """
        Check if an AWB already exists in the database.

        Args:
            awb_number (str): AWB number to check.

        Returns:
            tuple: Existing AWB record or None.
        """
        return self.find_record(
            "master_waybills",
            "awb_number = %s",
            (awb_number,),
            "awb_id, awb_number, status",
        )

    def save_master_waybill(self, data):
        """
        Save master waybill data.

        Args:
            data (dict): XFWB data.

        Returns:
            int: AWB ID of the saved record.
        """
        # Prepare master waybill data
        awb_data = {
            "awb_number": data["awb_number"],
            "type_code": data["type_code"],
            "origin_airport": data["origin_airport"],
            "destination_airport": data["destination_airport"],
            "total_pieces": data.get("total_pieces", 0),
            "total_weight": data.get("total_weight", 0.0),
            "weight_unit": data.get("weight_unit", "KGM"),
            "gross_volume": data.get("gross_volume"),
            "volume_unit": data.get("volume_unit"),
            "special_handling_code": data.get("special_handling_code"),
            "special_handling_codes": self.format_json_field(
                data.get("special_handling_codes")
            ),
            "goods_descriptions": self.format_json_field(
                data.get("goods_descriptions")
            ),
            "summary_description": data.get("summary_description"),
            "currency_code": data.get("currency_code"),
            "prepaid_collect_indicator": data.get("prepaid_collect_indicator", "P"),
            "is_mail": data.get("is_mail", False),
            "is_human_remains": data.get("is_human_remains", False),
            "is_partial": data.get("is_partial", False),
            "is_reconciled": False,
            "status": "PENDING",
            "branch_id": self.branch_id,
        }

        # Store complete original XML content for audit trails and reprocessing
        # Following OLD-PARSER format: json.dumps({'xml': xml_string})
        xml_content = data.get("xml_content")
        if xml_content:
            import json

            # Store XML content in OLD-PARSER compatible format
            awb_data["xml_data"] = json.dumps({"xml": xml_content})
            self.logger.info(
                f"Storing original XML content ({len(xml_content)} characters) for AWB {data['awb_number']}"
            )

        # Add timestamps and audit fields
        self.add_timestamps(awb_data, self.user_id, self.user_id)

        # Insert master waybill
        awb_id = self.insert_record("master_waybills", awb_data, "awb_id")

        self.logger.info(f"Saved master waybill with ID {awb_id}")
        return awb_id

    def update_master_waybill(self, awb_id, data):
        """
        Update existing master waybill data with XFWB information.

        Args:
            awb_id (int): Existing AWB ID to update.
            data (dict): XFWB data.

        Returns:
            bool: True if update was successful, False otherwise.
        """
        try:
            # Prepare update data (similar to OLD-PARSER logic)
            update_data = {
                "type_code": data["type_code"],
                "origin_airport": data["origin_airport"],
                "destination_airport": data["destination_airport"],
                "total_pieces": data.get("total_pieces", 0),
                "total_weight": data.get("total_weight", 0.0),
                "weight_unit": data.get("weight_unit", "KGM"),
                "gross_volume": data.get("gross_volume"),
                "volume_unit": data.get("volume_unit"),
                "special_handling_code": data.get("special_handling_code"),
                "special_handling_codes": self.format_json_field(
                    data.get("special_handling_codes")
                ),
                "goods_descriptions": self.format_json_field(
                    data.get("goods_descriptions")
                ),
                "summary_description": data.get("summary_description"),
                "currency_code": data.get("currency_code"),
                "prepaid_collect_indicator": data.get("prepaid_collect_indicator", "P"),
                "is_mail": data.get("is_mail", False),
                "is_human_remains": data.get("is_human_remains", False),
                "is_partial": data.get("is_partial", False),
            }

            # Store complete original XML content for audit trails and reprocessing
            # Following OLD-PARSER format: json.dumps({'xml': xml_string})
            xml_content = data.get("xml_content")
            if xml_content:
                import json

                # Store XML content in OLD-PARSER compatible format
                update_data["xml_data"] = json.dumps({"xml": xml_content})
                self.logger.info(
                    f"Updating XML content ({len(xml_content)} characters) for AWB {data['awb_number']}"
                )

            # Add timestamps and audit fields
            self.add_timestamps(update_data, updated_by=self.user_id)

            # Update master waybill
            rows_affected = self.update_record(
                "master_waybills", update_data, "awb_id = %s", (awb_id,)
            )

            if rows_affected > 0:
                self.logger.info(f"Updated master waybill with ID {awb_id}")
                return True
            else:
                self.logger.warning(f"No rows affected when updating AWB ID {awb_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error updating master waybill {awb_id}: {e}")
            return False

    def save_party_information(self, awb_id, data):
        """
        Save party information (shipper, consignee, agent, carrier).

        Args:
            awb_id (int): AWB ID.
            data (dict): XFWB data.
        """
        party_ids = {}

        # Save shipper
        shipper_data = data.get("shipper_data")
        if shipper_data:
            party_ids["shipper_code"] = self.find_or_create_shipper(shipper_data)

        # Save consignee
        consignee_data = data.get("consignee_data")
        if consignee_data:
            party_ids["consignee_code"] = self.find_or_create_consignee(consignee_data)

        # Save carrier
        carrier_data = data.get("carrier_data")
        if carrier_data:
            party_ids["carrier_code"] = self.find_or_create_carrier(carrier_data)

        # Update the master waybill with party IDs
        if party_ids:
            self.update_record("master_waybills", party_ids, "awb_id = %s", (awb_id,))
            self.logger.info(
                f"Updated AWB {awb_id} with party information: {party_ids}"
            )

        return party_ids

    def find_or_create_shipper(self, shipper_data):
        """
        Find or create a shipper record.

        Args:
            shipper_data (dict): Shipper data.

        Returns:
            int: Shipper ID.
        """
        name = shipper_data.get("name", "").strip()
        if not name:
            return None

        # Try to find existing shipper by name
        existing = self.find_record(
            "shippers", "LOWER(name) = LOWER(%s)", (name,), "id"
        )

        if existing:
            return existing[0]

        # Create new shipper
        shipper_record = {
            "name": name,
            "address_line1": shipper_data.get("address", ""),
            "city": shipper_data.get("city", ""),
            "country_code": shipper_data.get("country_code", ""),
            "postal_code": shipper_data.get("postal_code", ""),
            "is_active": True,
        }

        # Add timestamps (shippers table doesn't have created_by/updated_by columns)
        self.add_timestamps(shipper_record)

        shipper_id = self.insert_record("shippers", shipper_record, "id")
        self.logger.info(f"Created new shipper: {name} (ID: {shipper_id})")
        return shipper_id

    def find_or_create_consignee(self, consignee_data):
        """
        Find or create a consignee record.

        Args:
            consignee_data (dict): Consignee data.

        Returns:
            int: Consignee ID.
        """
        name = consignee_data.get("name", "").strip()
        if not name:
            return None

        # Try to find existing consignee by name
        existing = self.find_record(
            "consignees", "LOWER(name) = LOWER(%s)", (name,), "id"
        )

        if existing:
            return existing[0]

        # Create new consignee
        consignee_record = {
            "name": name,
            "address_line1": consignee_data.get("address", ""),
            "city": consignee_data.get("city", ""),
            "country_code": consignee_data.get("country_code", ""),
            "postal_code": consignee_data.get("postal_code", ""),
            "is_active": True,
        }

        # Add timestamps (consignees table doesn't have created_by/updated_by columns)
        self.add_timestamps(consignee_record)

        consignee_id = self.insert_record("consignees", consignee_record, "id")
        self.logger.info(f"Created new consignee: {name} (ID: {consignee_id})")
        return consignee_id

    def find_or_create_carrier(self, carrier_data):
        """
        Find or create a carrier record.

        Args:
            carrier_data (dict): Carrier data.

        Returns:
            int: Carrier ID.
        """
        name = carrier_data.get("name", "").strip()
        if not name:
            return None

        # Try to find existing carrier by name
        existing = self.find_record(
            "carrier_agents", "LOWER(name) = LOWER(%s)", (name,), "id"
        )

        if existing:
            return existing[0]

        # Create new carrier
        carrier_record = {
            "name": name,
            "address_line1": carrier_data.get("address", ""),
            "city": carrier_data.get("city", ""),
            "country_code": carrier_data.get("country_code", ""),
            "postal_code": carrier_data.get("postal_code", ""),
            "is_active": True,
        }

        # Add timestamps (carrier_agents table doesn't have created_by/updated_by columns)
        self.add_timestamps(carrier_record)

        carrier_id = self.insert_record("carrier_agents", carrier_record, "id")
        self.logger.info(f"Created new carrier: {name} (ID: {carrier_id})")
        return carrier_id

    def save_cargo_details(self, awb_id, data):
        """
        Save cargo details and handling instructions.

        Args:
            awb_id (int): AWB ID.
            data (dict): XFWB data.
        """
        # Save handling instructions if any special handling codes exist
        shc_codes = data.get("special_handling_codes", [])
        for code in shc_codes:
            self.save_handling_instruction(awb_id, code)

    def save_handling_instruction(self, awb_id, handling_code):
        """
        Save special handling code.

        Args:
            awb_id (int): AWB ID.
            handling_code (str): Special handling code.
        """
        # Check if the special handling code exists in the master table
        shc_exists = self.find_record(
            "special_handling_codes",
            "code = %s AND is_active = TRUE",
            (handling_code,),
            "id",
        )

        if not shc_exists:
            self.logger.warning(
                f"Special handling code '{handling_code}' not found in master table"
            )
            return None

        # Check if this AWB already has this SHC
        existing = self.find_record(
            "awb_special_handling_codes",
            "awb_id = %s AND code = %s AND is_house = FALSE",
            (awb_id, handling_code),
            "id",
        )

        if existing:
            self.logger.info(
                f"Special handling code {handling_code} already exists for AWB {awb_id}"
            )
            return existing[0]

        instruction_data = {
            "awb_id": awb_id,
            "code": handling_code,
            "is_house": False,
        }

        # Add timestamps (awb_special_handling_codes table has timestamps but no created_by/updated_by)
        self.add_timestamps(instruction_data)

        # Insert special handling code
        instruction_id = self.insert_record(
            "awb_special_handling_codes", instruction_data, "id"
        )

        self.logger.info(
            f"Saved special handling code {handling_code} with ID {instruction_id}"
        )
        return instruction_id

    def update_awb_status(self, awb_number, status, notes=None):
        """
        Update AWB status.

        Args:
            awb_number (str): AWB number.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            int: Number of affected rows.
        """
        update_data = {"status": status, "updated_by": self.user_id}

        if notes:
            update_data["status_notes"] = notes

        self.add_timestamps(update_data, updated_by=self.user_id)

        return self.update_record(
            "master_waybills", update_data, "awb_number = %s", (awb_number,)
        )

    def get_awb_summary(self, awb_number):
        """
        Get AWB summary information.

        Args:
            awb_number (str): AWB number.

        Returns:
            dict: AWB summary data.
        """
        # Get master waybill data with party IDs
        awb_data = self.find_record(
            "master_waybills",
            "awb_number = %s",
            (awb_number,),
            """awb_id, awb_number, type_code, origin_airport, destination_airport,
               total_pieces, total_weight, weight_unit, status, created_at,
               shipper_code, consignee_code, carrier_code""",
        )

        if not awb_data:
            return None

        summary = {
            "awb_id": awb_data[0],
            "awb_number": awb_data[1],
            "type_code": awb_data[2],
            "origin_airport": awb_data[3],
            "destination_airport": awb_data[4],
            "total_pieces": awb_data[5],
            "total_weight": awb_data[6],
            "weight_unit": awb_data[7],
            "status": awb_data[8],
            "created_at": awb_data[9],
        }

        # Get party information from respective tables
        summary["parties"] = {}

        # Get shipper info
        if awb_data[10]:  # shipper_code
            shipper = self.find_record(
                "shippers", "id = %s", (awb_data[10],), "name, city, country_code"
            )
            if shipper:
                summary["parties"]["SHIPPER"] = {
                    "name": shipper[0],
                    "city": shipper[1],
                    "country_code": shipper[2],
                }

        # Get consignee info
        if awb_data[11]:  # consignee_code
            consignee = self.find_record(
                "consignees", "id = %s", (awb_data[11],), "name, city, country_code"
            )
            if consignee:
                summary["parties"]["CONSIGNEE"] = {
                    "name": consignee[0],
                    "city": consignee[1],
                    "country_code": consignee[2],
                }

        # Get carrier info
        if awb_data[12]:  # carrier_code
            carrier = self.find_record(
                "carrier_agents", "id = %s", (awb_data[12],), "name, city, country_code"
            )
            if carrier:
                summary["parties"]["CARRIER"] = {
                    "name": carrier[0],
                    "city": carrier[1],
                    "country_code": carrier[2],
                }

        return summary
