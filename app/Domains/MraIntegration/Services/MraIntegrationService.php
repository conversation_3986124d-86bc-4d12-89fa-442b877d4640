<?php

namespace App\Domains\MraIntegration\Services;

use App\Models\FlightManifest;
use App\Models\MasterWaybill;
use App\Models\HouseWaybill;
use App\Models\MraApiLog;
use App\Services\MraApi\MraApiClient;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Notification;
use App\Notifications\MraApiSyncFailed;
use Throwable;

class MraIntegrationService
{
    /**
     * MRA API Client
     *
     * @var MraApiClient
     */
    protected $mraApiClient;

    /**
     * Whether to use SOAP client instead of REST API
     *
     * @var bool
     */
    protected $useSoapClient;

    /**
     * SOAP client URL
     *
     * @var string
     */
    protected $soapClientUrl;

    /**
     * Create a new MRA integration service instance.
     *
     * @param MraApiClient $mraApiClient
     * @return void
     */
    public function __construct(MraApiClient $mraApiClient)
    {
        $this->mraApiClient = $mraApiClient;
        $this->useSoapClient = config('services.mra.use_soap_client', false);
        $this->soapClientUrl = config('services.mra.soap_client_url', 'http://localhost:8004');
    }

    /**
     * Get master waybills with their sync status
     *
     * @param string|null $status Filter by sync status
     * @return array
     */
    public function getMasterWaybills($status = null)
    {
        try {
            $query = MasterWaybill::with('flightManifest')->orderBy('created_at', 'desc');

            // Filter by sync status if provided
            if ($status) {
                $query->where('sync_status', $status);
            }

            $waybills = $query->get();

            $formattedData = [];
            foreach ($waybills as $waybill) {
                // Get the latest sync log for this waybill
                $latestLog = MraApiLog::where('message_type', 'XFWB')
                    ->where(function ($q) use ($waybill) {
                        $q->where('manifest_id', $waybill->manifest_id)
                            ->orWhereJsonContains('request_data->awbNumber', $waybill->awb_number);
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();

                // Get the flight manifest for this waybill
                $manifest = $waybill->flightManifest;

                $formattedData[] = [
                    'awb_number' => $waybill->awb_number,
                    'manifest_id' => $waybill->manifest_id,
                    'flight_number' => $manifest ? $manifest->flight_number : 'N/A',
                    'flight_date' => $manifest && $manifest->flight_date ? $manifest->flight_date->format('d-m-Y') : 'N/A',
                    'origin' => $waybill->origin,
                    'destination' => $waybill->destination,
                    'pieces' => $waybill->pieces,
                    'weight' => $waybill->weight,
                    'sync_status' => $waybill->sync_status ?: 'PENDING',
                    'sync_timestamp' => $waybill->sync_timestamp ? $waybill->sync_timestamp->format('d-m-Y H:i:s') : 'N/A',
                    'sync_client_id' => $waybill->sync_client_id ?? 'N/A',
                    'latest_log' => $latestLog,
                    'status' => $waybill->status,
                ];
            }

            return [
                'success' => true,
                'waybills' => $formattedData
            ];
        } catch (\Exception $e) {
            Log::error('Error getting master waybills', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error getting master waybills: ' . $e->getMessage(),
                'waybills' => []
            ];
        }
    }

    /**
     * Get house waybills with their sync status
     *
     * @param string|null $status Filter by sync status
     * @return array
     */
    public function getHouseWaybills($status = null)
    {
        try {
            $query = HouseWaybill::with('flightManifest')->orderBy('created_at', 'desc');

            // Filter by sync status if provided
            if ($status) {
                $query->where('sync_status', $status);
            }

            $waybills = $query->get();

            $formattedData = [];
            foreach ($waybills as $waybill) {
                // Get the latest sync log for this waybill
                $latestLog = MraApiLog::where('message_type', 'XFZB')
                    ->where(function ($q) use ($waybill) {
                        $q->where('manifest_id', $waybill->manifest_id)
                            ->orWhereJsonContains('request_data->hawbNumber', $waybill->hawb_number);
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();

                // Get the flight manifest for this waybill
                $manifest = $waybill->flightManifest;

                $formattedData[] = [
                    'hawb_number' => $waybill->hawb_number,
                    'mawb_number' => $waybill->mawb_number,
                    'manifest_id' => $waybill->manifest_id,
                    'flight_number' => $manifest ? $manifest->flight_number : 'N/A',
                    'flight_date' => $manifest && $manifest->flight_date ? $manifest->flight_date->format('d-m-Y') : 'N/A',
                    'origin' => $waybill->origin,
                    'destination' => $waybill->destination,
                    'pieces' => $waybill->pieces,
                    'weight' => $waybill->weight,
                    'sync_status' => $waybill->sync_status ?: 'PENDING',
                    'sync_timestamp' => $waybill->sync_timestamp ? $waybill->sync_timestamp->format('d-m-Y H:i:s') : 'N/A',
                    'sync_client_id' => $waybill->sync_client_id ?? 'N/A',
                    'latest_log' => $latestLog,
                    'status' => $waybill->status,
                ];
            }

            return [
                'success' => true,
                'waybills' => $formattedData
            ];
        } catch (\Exception $e) {
            Log::error('Error getting house waybills', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error getting house waybills: ' . $e->getMessage(),
                'waybills' => []
            ];
        }
    }

    /**
     * Get sync logs for a specific manifest
     *
     * @param string $manifestId
     * @return array
     */
    public function getSyncLogs($manifestId)
    {
        try {
            $logs = MraApiLog::where('manifest_id', $manifestId)
                ->orderBy('created_at', 'desc')
                ->get();

            return [
                'success' => true,
                'logs' => $logs // Return the collection directly, not as an array
            ];
        } catch (\Exception $e) {
            Log::error('Error getting sync logs', [
                'manifest_id' => $manifestId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error getting sync logs: ' . $e->getMessage(),
                'logs' => collect([]) // Return an empty collection instead of an empty array
            ];
        }
    }

    /**
     * Get all sync logs
     *
     * @param string|null $status Filter by status
     * @return array
     */
    public function getAllSyncLogs($status = null)
    {
        try {
            $query = MraApiLog::with('flightManifest')
                ->orderBy('created_at', 'desc');

            // Filter by status if provided
            if ($status) {
                $query->where('status', $status);
            }

            $logs = $query->get();

            return [
                'success' => true,
                'logs' => $logs // Return the collection directly, not as an array
            ];
        } catch (\Exception $e) {
            Log::error('Error getting all sync logs', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error getting all sync logs: ' . $e->getMessage(),
                'logs' => collect([]) // Return an empty collection instead of an empty array
            ];
        }
    }

    /**
     * Sync a master waybill with MRA
     *
     * @param string $awbNumber
     * @param int $retryCount Maximum number of retry attempts
     * @return array
     */
    public function syncMasterWaybill($awbNumber, $retryCount = 3)
    {
        try {
            // Get the master waybill
            $waybill = MasterWaybill::where('awb_number', $awbNumber)->first();

            if (!$waybill) {
                Log::warning("MRA API sync failed: Master waybill not found", ['awb_number' => $awbNumber]);
                return [
                    'success' => false,
                    'message' => "Master waybill not found: {$awbNumber}",
                    'http_status' => 404
                ];
            }

            // Check if the waybill has XML data
            if (!$waybill->xml_data || !isset($waybill->xml_data['xml'])) {
                Log::warning("MRA API sync failed: Invalid XML data", ['awb_number' => $awbNumber]);
                return [
                    'success' => false,
                    'message' => "Invalid XML data for waybill: {$awbNumber}",
                    'http_status' => 400
                ];
            }

            // Get the flight manifest for this waybill
            $manifest = $waybill->flightManifest;
            $manifestId = $waybill->manifest_id;
            $flightNumber = $manifest ? $manifest->flight_number : 'UNKNOWN';
            $departureDate = $manifest ? $manifest->flight_date : now();

            // Create a unique log ID
            $logId = 'MRA-' . date('YmdHis') . '-' . Str::random(6);

            // Create a new log entry
            $log = new MraApiLog();
            $log->log_id = $logId;
            $log->manifest_id = $manifestId;
            $log->message_type = 'XFWB';
            $log->flight_number = $flightNumber;
            $log->departure_date = $departureDate;
            $log->status = 'PENDING';
            $log->attempt_count = 1;
            $log->created_by = Auth::id() ?? 1; // Default to system user if not authenticated
            $log->save();

            // Prepare request data for logging (using camelCase to match actual API payload)
            $requestData = [
                'airWaybillId' => $awbNumber,
                'messageType' => 'XFWB',
                'departureDate' => $departureDate->format('Y-m-d\TH:i:s.v\Z'),
                'jsonData' => $this->mraApiClient->convertXmlToCleanJson($waybill->xml_data['xml'], 'XFWB'),
                'flightNumber' => $flightNumber
            ];

            // Update log with request data
            $log->request_data = $requestData;
            $log->save();

            // Start timing the API call
            $startTime = microtime(true);

            // Submit to MRA API with retry mechanism
            $result = null;
            $attempt = 0;
            $lastException = null;

            while ($attempt < $retryCount) {
                try {
                    $attempt++;

                    // Submit to MRA API or SOAP client based on configuration
                    if ($this->useSoapClient) {
                        $result = $this->submitViaSoapClient(
                            'XFWB',
                            $awbNumber,
                            $waybill->xml_data['xml']
                        );
                    } else {
                        $result = $this->mraApiClient->submitWaybill(
                            $manifestId,
                            'XFWB',
                            $departureDate->format('Y-m-d\TH:i:s.v\Z'),
                            $waybill->xml_data['xml'], // Use the raw XML data
                            $flightNumber,
                            $awbNumber
                        );
                    }

                    // If successful, break the retry loop
                    if ($result['success']) {
                        break;
                    }

                    // If this is a transient error, retry
                    $isTransientError = $this->isTransientError($result);
                    if (!$isTransientError) {
                        break;
                    }

                    // Log retry attempt
                    Log::info("MRA API sync retry for transient error", [
                        'awb_number' => $awbNumber,
                        'attempt' => $attempt,
                        'max_attempts' => $retryCount,
                        'error' => $result['message'] ?? 'Unknown error'
                    ]);

                    // Wait before retrying (exponential backoff)
                    $waitTime = pow(2, $attempt - 1) * 1000000; // in microseconds (1s, 2s, 4s)
                    usleep($waitTime);
                } catch (\Exception $e) {
                    $lastException = $e;

                    // Log retry attempt
                    Log::warning("MRA API sync exception, retrying", [
                        'awb_number' => $awbNumber,
                        'attempt' => $attempt,
                        'max_attempts' => $retryCount,
                        'exception' => $e->getMessage()
                    ]);

                    // Wait before retrying (exponential backoff)
                    $waitTime = pow(2, $attempt - 1) * 1000000; // in microseconds (1s, 2s, 4s)
                    usleep($waitTime);
                }
            }

            // End timing the API call
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // in milliseconds

            // If we still don't have a result after all retries, handle the exception
            if ($result === null && $lastException !== null) {
                Log::error("MRA API sync failed after {$retryCount} attempts", [
                    'awb_number' => $awbNumber,
                    'exception' => $lastException->getMessage(),
                    'trace' => $lastException->getTraceAsString()
                ]);

                // Update log with error
                $log->status = 'FAILED';
                $log->error_message = "Exception after {$attempt} attempts: " . $lastException->getMessage();
                $log->last_attempt_at = now();
                $log->response_time = $responseTime;
                $log->attempt_count = $attempt;
                $log->save();

                // Update waybill sync status
                $waybill->sync_status = 'FAILED';
                $waybill->sync_timestamp = now();
                $waybill->save();

                return [
                    'success' => false,
                    'message' => "Failed to sync waybill after {$attempt} attempts: " . $lastException->getMessage(),
                    'log' => $log,
                    'http_status' => 500
                ];
            }

            // Update log with response
            $log->response_data = $result;
            $log->last_attempt_at = now();
            $log->response_time = $responseTime;
            $log->attempt_count = $attempt;

            if ($result['success']) {
                $log->status = 'SUCCESS';

                // Update waybill sync status
                $waybill->sync_status = 'SUCCESS';
                $waybill->sync_timestamp = now();
                $waybill->sync_client_id = 'MRA-API';
                $waybill->save();

                Log::info("MRA API sync successful", [
                    'awb_number' => $awbNumber,
                    'log_id' => $logId,
                    'response_time' => $responseTime,
                    'attempts' => $attempt
                ]);
            } else {
                $log->status = 'FAILED';
                $log->error_message = $result['message'] ?? 'Unknown error';

                // Update waybill sync status
                $waybill->sync_status = 'FAILED';
                $waybill->sync_timestamp = now();
                $waybill->save();

                Log::error("MRA API sync failed", [
                    'awb_number' => $awbNumber,
                    'log_id' => $logId,
                    'error' => $log->error_message,
                    'response_time' => $responseTime,
                    'attempts' => $attempt,
                    'response' => $result
                ]);

                // Send notification for failed sync
                try {
                    if (config('services.mra.notifications_enabled', false)) {
                        $recipients = config('services.mra.notification_recipients', []);
                        if (!empty($recipients)) {
                            Notification::route('mail', $recipients)
                                ->notify(new MraApiSyncFailed($log));
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to send MRA API sync failure notification", [
                        'exception' => $e->getMessage()
                    ]);
                }
            }

            $log->save();

            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'Waybill synced successfully' : ($result['message'] ?? 'Failed to sync waybill'),
                'log' => $log,
                'http_status' => $result['success'] ? 200 : ($result['status'] ?? 500),
                'response_time' => $responseTime,
                'attempts' => $attempt
            ];
        } catch (\Exception $e) {
            Log::error('Error syncing master waybill', [
                'awb_number' => $awbNumber,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error syncing master waybill: ' . $e->getMessage(),
                'http_status' => 500
            ];
        }
    }

    /**
     * Check if an error is transient and can be retried
     *
     * @param array $result
     * @return bool
     */
    protected function isTransientError($result)
    {
        // If the result is successful, it's not an error
        if ($result['success']) {
            return false;
        }

        // Check for specific error messages that indicate transient errors
        $transientErrorPatterns = [
            '/timeout/i',
            '/connection refused/i',
            '/network error/i',
            '/temporarily unavailable/i',
            '/too many requests/i',
            '/rate limit/i',
            '/server error/i',
            '/internal server error/i',
            '/503/i',
            '/504/i',
            '/502/i'
        ];

        $errorMessage = $result['message'] ?? '';

        foreach ($transientErrorPatterns as $pattern) {
            if (preg_match($pattern, $errorMessage)) {
                return true;
            }
        }

        // Check for specific HTTP status codes that indicate transient errors
        $transientStatusCodes = [429, 500, 502, 503, 504];

        $statusCode = $result['status'] ?? 0;

        return in_array($statusCode, $transientStatusCodes);
    }

    /**
     * Sync a house waybill with MRA
     *
     * @param string $hawbNumber
     * @param int $retryCount Maximum number of retry attempts
     * @return array
     */
    public function syncHouseWaybill($hawbNumber, $retryCount = 3)
    {
        try {
            // Get the house waybill
            $waybill = HouseWaybill::where('hawb_number', $hawbNumber)->first();

            if (!$waybill) {
                Log::warning("MRA API sync failed: House waybill not found", ['hawb_number' => $hawbNumber]);
                return [
                    'success' => false,
                    'message' => "House waybill not found: {$hawbNumber}",
                    'http_status' => 404
                ];
            }

            // Check if the waybill has XML data
            if (!$waybill->xml_data || !isset($waybill->xml_data['xml'])) {
                Log::warning("MRA API sync failed: Invalid XML data", ['hawb_number' => $hawbNumber]);
                return [
                    'success' => false,
                    'message' => "Invalid XML data for house waybill: {$hawbNumber}",
                    'http_status' => 400
                ];
            }

            // Get the flight manifest for this house waybill
            $manifestId = $waybill->manifest_id;
            $manifest = $waybill->flightManifest;

            $flightNumber = $manifest ? $manifest->flight_number : 'UNKNOWN';
            $departureDate = $manifest ? $manifest->flight_date : now();

            // Create a unique log ID
            $logId = 'MRA-' . date('YmdHis') . '-' . Str::random(6);

            // Create a new log entry
            $log = new MraApiLog();
            $log->log_id = $logId;
            $log->manifest_id = $manifestId;
            $log->message_type = 'XFZB';
            $log->flight_number = $flightNumber;
            $log->departure_date = $departureDate;
            $log->status = 'PENDING';
            $log->attempt_count = 1;
            $log->created_by = Auth::id() ?? 1; // Default to system user if not authenticated
            $log->save();

            // Prepare request data for logging (using camelCase to match actual API payload)
            $jsonDataString = $this->mraApiClient->convertXmlToCleanJson($waybill->xml_data['xml'], 'XFZB');
            $jsonDataArray = json_decode($jsonDataString, true);

            $requestData = [
                'airWaybillId' => $hawbNumber, // Using airWaybillId for HAWB
                'messageType' => 'XFZB',
                'departureDate' => $departureDate->format('Y-m-d\TH:i:s.v\Z'), // ISO 8601 format
                'jsonData' => $jsonDataArray ?: $jsonDataString, // Use array if decode successful, otherwise string
                'flightNumber' => $flightNumber
            ];

            // Update log with request data
            $log->request_data = $requestData;
            $log->save();

            // Start timing the API call
            $startTime = microtime(true);

            // Submit to MRA API with retry mechanism
            $result = null;
            $attempt = 0;
            $lastException = null;

            while ($attempt < $retryCount) {
                try {
                    $attempt++;

                    // Submit to MRA API or SOAP client based on configuration
                    if ($this->useSoapClient) {
                        $result = $this->submitViaSoapClient(
                            'XFZB',
                            $hawbNumber,
                            $waybill->xml_data['xml']
                        );
                    } else {
                        $result = $this->mraApiClient->submitWaybill(
                            $manifestId,
                            'XFZB',
                            $departureDate->format('Y-m-d\TH:i:s.v\Z'), // ISO 8601 format
                            $waybill->xml_data['xml'], // Use the raw XML data
                            $flightNumber,
                            $hawbNumber
                        );
                    }

                    // If successful, break the retry loop
                    if ($result['success']) {
                        break;
                    }

                    // If this is a transient error, retry
                    $isTransientError = $this->isTransientError($result);
                    if (!$isTransientError) {
                        break;
                    }

                    // Log retry attempt
                    Log::info("MRA API sync retry for transient error", [
                        'hawb_number' => $hawbNumber,
                        'attempt' => $attempt,
                        'max_attempts' => $retryCount,
                        'error' => $result['message'] ?? 'Unknown error'
                    ]);

                    // Wait before retrying (exponential backoff)
                    $waitTime = pow(2, $attempt - 1) * 1000000; // in microseconds (1s, 2s, 4s)
                    usleep($waitTime);
                } catch (\Exception $e) {
                    $lastException = $e;

                    // Log retry attempt
                    Log::warning("MRA API sync exception, retrying", [
                        'hawb_number' => $hawbNumber,
                        'attempt' => $attempt,
                        'max_attempts' => $retryCount,
                        'exception' => $e->getMessage()
                    ]);

                    // Wait before retrying (exponential backoff)
                    $waitTime = pow(2, $attempt - 1) * 1000000; // in microseconds (1s, 2s, 4s)
                    usleep($waitTime);
                }
            }

            // End timing the API call
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // in milliseconds

            // If we still don't have a result after all retries, handle the exception
            if ($result === null && $lastException !== null) {
                Log::error("MRA API sync failed after {$retryCount} attempts", [
                    'hawb_number' => $hawbNumber,
                    'exception' => $lastException->getMessage(),
                    'trace' => $lastException->getTraceAsString()
                ]);

                // Update log with error
                $log->status = 'FAILED';
                $log->error_message = "Exception after {$attempt} attempts: " . $lastException->getMessage();
                $log->last_attempt_at = now();
                $log->response_time = $responseTime;
                $log->attempt_count = $attempt;
                $log->save();

                // Update waybill sync status
                $waybill->sync_status = 'FAILED';
                $waybill->sync_timestamp = now();
                $waybill->save();

                return [
                    'success' => false,
                    'message' => "Failed to sync house waybill after {$attempt} attempts: " . $lastException->getMessage(),
                    'log' => $log,
                    'http_status' => 500
                ];
            }

            // Update log with response
            $log->response_data = $result;
            $log->last_attempt_at = now();
            $log->response_time = $responseTime;
            $log->attempt_count = $attempt;

            if ($result['success']) {
                $log->status = 'SUCCESS';

                // Update waybill sync status
                $waybill->sync_status = 'SUCCESS';
                $waybill->sync_timestamp = now();
                $waybill->sync_client_id = 'MRA-API';
                $waybill->save();

                Log::info("MRA API sync successful", [
                    'hawb_number' => $hawbNumber,
                    'log_id' => $logId,
                    'response_time' => $responseTime,
                    'attempts' => $attempt
                ]);
            } else {
                $log->status = 'FAILED';
                $log->error_message = $result['message'] ?? 'Unknown error';

                // Update waybill sync status
                $waybill->sync_status = 'FAILED';
                $waybill->sync_timestamp = now();
                $waybill->save();

                Log::error("MRA API sync failed", [
                    'hawb_number' => $hawbNumber,
                    'log_id' => $logId,
                    'error' => $log->error_message,
                    'response_time' => $responseTime,
                    'attempts' => $attempt,
                    'response' => $result
                ]);

                // Send notification for failed sync
                try {
                    if (config('services.mra.notifications_enabled', false)) {
                        $recipients = config('services.mra.notification_recipients', []);
                        if (!empty($recipients)) {
                            Notification::route('mail', $recipients)
                                ->notify(new MraApiSyncFailed($log));
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to send MRA API sync failure notification", [
                        'exception' => $e->getMessage()
                    ]);
                }
            }

            $log->save();

            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'House waybill synced successfully' : ($result['message'] ?? 'Failed to sync house waybill'),
                'log' => $log,
                'http_status' => $result['success'] ? 200 : ($result['status'] ?? 500),
                'response_time' => $responseTime,
                'attempts' => $attempt
            ];
        } catch (\Exception $e) {
            Log::error('Error syncing house waybill', [
                'hawb_number' => $hawbNumber,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error syncing house waybill: ' . $e->getMessage(),
                'http_status' => 500
            ];
        }
    }

    /**
     * Retry a failed sync
     *
     * @param string $logId
     * @return array
     */
    public function retrySyncLog($logId)
    {
        try {
            // Get the log entry
            $log = MraApiLog::where('log_id', $logId)->first();

            if (!$log) {
                Log::warning("MRA API retry failed: Log entry not found", ['log_id' => $logId]);
                return [
                    'success' => false,
                    'message' => "Log entry not found: {$logId}"
                ];
            }

            // Increment attempt count
            $log->attempt_count += 1;
            $log->save();

            // Determine the type of message and sync accordingly
            $messageType = $log->message_type;

            if ($messageType === 'XFWB') {
                // Find the master waybill
                $waybill = MasterWaybill::where('manifest_id', $log->manifest_id)
                    ->first();

                if (!$waybill) {
                    Log::warning("MRA API retry failed: Master waybill not found", [
                        'log_id' => $logId,
                        'manifest_id' => $log->manifest_id
                    ]);
                    return [
                        'success' => false,
                        'message' => "Master waybill not found for manifest: {$log->manifest_id}"
                    ];
                }

                return $this->syncMasterWaybill($waybill->awb_number);
            } elseif ($messageType === 'XFZB') {
                // Find the house waybill
                $waybill = HouseWaybill::where('manifest_id', $log->manifest_id)
                    ->first();

                if (!$waybill) {
                    Log::warning("MRA API retry failed: House waybill not found", [
                        'log_id' => $logId,
                        'manifest_id' => $log->manifest_id
                    ]);
                    return [
                        'success' => false,
                        'message' => "House waybill not found for manifest: {$log->manifest_id}"
                    ];
                }

                return $this->syncHouseWaybill($waybill->hawb_number);
            } else {
                Log::warning("MRA API retry failed: Unsupported message type", [
                    'log_id' => $logId,
                    'message_type' => $messageType
                ]);
                return [
                    'success' => false,
                    'message' => "Unsupported message type: {$messageType}"
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error retrying sync', [
                'log_id' => $logId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error retrying sync: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check MRA API health
     *
     * @return array
     */
    public function checkApiHealth()
    {
        try {
            // Initialize default response structure
            $response = [
                'success' => false,
                'auth_status' => false,
                'response_time' => 0,
                'token_status' => [
                    'exists' => false,
                    'valid' => false,
                    'expires_in' => null
                ],
                'sync_stats' => [
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0,
                    'success_rate' => 0
                ],
                'recent_errors' => []
            ];

            // Check if we have a valid token in cache
            $tokenData = Cache::get('mra_api_token');

            if ($tokenData && isset($tokenData['expires']) && isset($tokenData['token'])) {
                $response['token_status']['exists'] = true;
                $expiresAt = $tokenData['expires'];
                $now = time();
                $response['token_status']['valid'] = $expiresAt > $now;
                $response['token_status']['expires_in'] = $expiresAt - $now;
            }

            // Try to authenticate
            $startTime = microtime(true);
            $authResult = $this->mraApiClient->authenticate();
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // in milliseconds

            $response['auth_status'] = $authResult;
            $response['response_time'] = $responseTime;

            // Get recent sync statistics
            try {
                $totalLogs = MraApiLog::where('created_at', '>=', now()->subDays(7))->count();
                $successLogs = MraApiLog::where('created_at', '>=', now()->subDays(7))
                    ->where('status', 'SUCCESS')
                    ->count();
                $failedLogs = MraApiLog::where('created_at', '>=', now()->subDays(7))
                    ->where('status', 'FAILED')
                    ->count();

                $successRate = $totalLogs > 0 ? round(($successLogs / $totalLogs) * 100, 2) : 0;

                $response['sync_stats'] = [
                    'total' => $totalLogs,
                    'success' => $successLogs,
                    'failed' => $failedLogs,
                    'success_rate' => $successRate
                ];
            } catch (\Exception $e) {
                Log::warning('Error getting sync statistics', [
                    'exception' => $e->getMessage()
                ]);
                // Keep default sync_stats in response
            }

            // Get recent errors
            try {
                $recentErrors = MraApiLog::where('created_at', '>=', now()->subDays(1))
                    ->where('status', 'FAILED')
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get();

                if ($recentErrors && $recentErrors->count() > 0) {
                    $response['recent_errors'] = $recentErrors->map(function ($log) {
                        return [
                            'log_id' => $log->log_id,
                            'message_type' => $log->message_type,
                            'error_message' => $log->error_message,
                            'timestamp' => $log->created_at->format('Y-m-d H:i:s')
                        ];
                    })->toArray();
                }
            } catch (\Exception $e) {
                Log::warning('Error getting recent errors', [
                    'exception' => $e->getMessage()
                ]);
                // Keep default recent_errors in response
            }

            // Set success to true if we got this far
            $response['success'] = true;

            return $response;
        } catch (\Exception $e) {
            Log::error('Error checking MRA API health', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error checking MRA API health: ' . $e->getMessage(),
                'auth_status' => false,
                'token_status' => [
                    'exists' => false,
                    'valid' => false,
                    'expires_in' => null
                ],
                'sync_stats' => [
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0,
                    'success_rate' => 0
                ],
                'recent_errors' => []
            ];
        }
    }

    /**
     * Sync all master waybills with specified status
     *
     * @param string $status Filter by sync status (PENDING, FAILED, or ALL)
     * @return array
     */
    public function syncAllMasterWaybills($status = 'PENDING')
    {
        try {
            // Build query for master waybills
            $query = MasterWaybill::with('flightManifest');

            // Filter by sync status if not 'ALL'
            if ($status !== 'ALL') {
                $query->where('sync_status', $status);
            }

            $masterWaybills = $query->get();

            $results = [
                'total' => $masterWaybills->count(),
                'success' => 0,
                'failed' => 0,
                'details' => []
            ];

            Log::info("MRA API sync all master waybills started", [
                'status_filter' => $status,
                'total_waybills' => $results['total']
            ]);

            // Sync each master waybill
            foreach ($masterWaybills as $waybill) {
                $result = $this->syncMasterWaybill($waybill->awb_number);

                if ($result['success']) {
                    $results['success']++;
                } else {
                    $results['failed']++;
                }

                $results['details'][] = [
                    'awb_number' => $waybill->awb_number,
                    'manifest_id' => $waybill->manifest_id,
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                // Add a small delay to avoid overwhelming the API
                usleep(500000); // 0.5 seconds
            }

            // Log the results
            Log::info("MRA API sync all master waybills completed", [
                'status_filter' => $status,
                'total_waybills' => $results['total'],
                'success' => $results['success'],
                'failed' => $results['failed']
            ]);

            return [
                'success' => true,
                'message' => "Master waybill sync completed. Total: {$results['total']}, Success: {$results['success']}, Failed: {$results['failed']}",
                'results' => $results
            ];
        } catch (\Exception $e) {
            Log::error("MRA API sync all master waybills failed", [
                'status_filter' => $status,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error syncing master waybills: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Sync all house waybills with specified status
     *
     * @param string $status Filter by sync status (PENDING, FAILED, or ALL)
     * @return array
     */
    public function syncAllHouseWaybills($status = 'PENDING')
    {
        try {
            // Build query for house waybills
            $query = HouseWaybill::with('flightManifest');

            // Filter by sync status if not 'ALL'
            if ($status !== 'ALL') {
                $query->where('sync_status', $status);
            }

            $houseWaybills = $query->get();

            $results = [
                'total' => $houseWaybills->count(),
                'success' => 0,
                'failed' => 0,
                'details' => []
            ];

            Log::info("MRA API sync all house waybills started", [
                'status_filter' => $status,
                'total_waybills' => $results['total']
            ]);

            // Sync each house waybill
            foreach ($houseWaybills as $waybill) {
                $result = $this->syncHouseWaybill($waybill->hawb_number);

                if ($result['success']) {
                    $results['success']++;
                } else {
                    $results['failed']++;
                }

                $results['details'][] = [
                    'hawb_number' => $waybill->hawb_number,
                    'mawb_number' => $waybill->mawb_number,
                    'manifest_id' => $waybill->manifest_id,
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                // Add a small delay to avoid overwhelming the API
                usleep(500000); // 0.5 seconds
            }

            // Log the results
            Log::info("MRA API sync all house waybills completed", [
                'status_filter' => $status,
                'total_waybills' => $results['total'],
                'success' => $results['success'],
                'failed' => $results['failed']
            ]);

            return [
                'success' => true,
                'message' => "House waybill sync completed. Total: {$results['total']}, Success: {$results['success']}, Failed: {$results['failed']}",
                'results' => $results
            ];
        } catch (\Exception $e) {
            Log::error("MRA API sync all house waybills failed", [
                'status_filter' => $status,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error syncing house waybills: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Sync all waybills for a flight manifest
     *
     * @param string $manifestId
     * @return array
     */
    public function syncAllWaybillsForManifest($manifestId)
    {
        try {
            // Get the flight manifest
            $manifest = FlightManifest::where('manifest_id', $manifestId)->first();

            if (!$manifest) {
                Log::warning("MRA API sync all failed: Flight manifest not found", ['manifest_id' => $manifestId]);
                return [
                    'success' => false,
                    'message' => "Flight manifest not found: {$manifestId}"
                ];
            }

            // Get all master waybills for this manifest
            $masterWaybills = MasterWaybill::where('manifest_id', $manifestId)->get();

            // Get all house waybills for this manifest
            $houseWaybills = HouseWaybill::where('manifest_id', $manifestId)->get();

            $results = [
                'master_waybills' => [
                    'total' => $masterWaybills->count(),
                    'success' => 0,
                    'failed' => 0,
                    'details' => []
                ],
                'house_waybills' => [
                    'total' => $houseWaybills->count(),
                    'success' => 0,
                    'failed' => 0,
                    'details' => []
                ]
            ];

            // Sync master waybills
            foreach ($masterWaybills as $waybill) {
                $result = $this->syncMasterWaybill($waybill->awb_number);

                if ($result['success']) {
                    $results['master_waybills']['success']++;
                } else {
                    $results['master_waybills']['failed']++;
                }

                $results['master_waybills']['details'][] = [
                    'awb_number' => $waybill->awb_number,
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                // Add a small delay to avoid overwhelming the API
                usleep(500000); // 0.5 seconds
            }

            // Sync house waybills
            foreach ($houseWaybills as $waybill) {
                $result = $this->syncHouseWaybill($waybill->hawb_number);

                if ($result['success']) {
                    $results['house_waybills']['success']++;
                } else {
                    $results['house_waybills']['failed']++;
                }

                $results['house_waybills']['details'][] = [
                    'hawb_number' => $waybill->hawb_number,
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                // Add a small delay to avoid overwhelming the API
                usleep(500000); // 0.5 seconds
            }

            $totalSuccess = $results['master_waybills']['success'] + $results['house_waybills']['success'];
            $totalFailed = $results['master_waybills']['failed'] + $results['house_waybills']['failed'];
            $totalWaybills = $results['master_waybills']['total'] + $results['house_waybills']['total'];

            // Log the results
            Log::info("MRA API sync all completed", [
                'manifest_id' => $manifestId,
                'total_waybills' => $totalWaybills,
                'success' => $totalSuccess,
                'failed' => $totalFailed
            ]);

            return [
                'success' => true,
                'message' => "Sync completed. Total: {$totalWaybills}, Success: {$totalSuccess}, Failed: {$totalFailed}",
                'results' => $results
            ];
        } catch (\Exception $e) {
            Log::error('Error syncing all waybills for manifest', [
                'manifest_id' => $manifestId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error syncing all waybills: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Force refresh the MRA API authentication token
     *
     * @return array
     */
    public function refreshAuthToken()
    {
        try {
            // Force refresh the token
            $result = $this->mraApiClient->authenticate(true);

            if ($result) {
                Log::info('MRA API token refreshed successfully');

                // Get token data from cache
                $tokenData = Cache::get('mra_api_token');
                $expiresIn = null;

                if ($tokenData && isset($tokenData['expires'])) {
                    $expiresIn = $tokenData['expires'] - time();
                }

                return [
                    'success' => true,
                    'message' => 'Authentication token refreshed successfully',
                    'expires_in' => $expiresIn
                ];
            } else {
                Log::error('Failed to refresh MRA API token');

                return [
                    'success' => false,
                    'message' => 'Failed to refresh authentication token'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error refreshing MRA API token', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error refreshing authentication token: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Submit XML data via SOAP client
     *
     * @param string $messageType
     * @param string $waybillNumber
     * @param string $xmlData
     * @return array
     */
    protected function submitViaSoapClient($messageType, $waybillNumber, $xmlData)
    {
        try {
            Log::info("Submitting {$messageType} via SOAP client", [
                'waybill_number' => $waybillNumber,
                'soap_client_url' => $this->soapClientUrl
            ]);

            // Submit to SOAP client
            $response = Http::timeout(60)->post("{$this->soapClientUrl}/submit", [
                'record_type' => $messageType,
                'record_id' => $waybillNumber,
                'xml_data' => $xmlData,
                'user_id' => Auth::id() ?? 1,
                'airline_code' => 'AC' // Let the SOAP client extract the correct airline code
            ]);

            if ($response->successful()) {
                $result = $response->json();

                Log::info("SOAP client submission result", [
                    'waybill_number' => $waybillNumber,
                    'message_type' => $messageType,
                    'success' => $result['success'] ?? false,
                    'message' => $result['message'] ?? 'No message'
                ]);

                return [
                    'success' => $result['success'] ?? false,
                    'message' => $result['message'] ?? 'Submission completed',
                    'status' => $response->status(),
                    'response_data' => $result
                ];
            } else {
                Log::error("SOAP client submission failed", [
                    'waybill_number' => $waybillNumber,
                    'message_type' => $messageType,
                    'http_status' => $response->status(),
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to communicate with SOAP service',
                    'status' => $response->status(),
                    'response_data' => null
                ];
            }
        } catch (\Exception $e) {
            Log::error("SOAP client submission exception", [
                'waybill_number' => $waybillNumber,
                'message_type' => $messageType,
                'exception' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'SOAP client error: ' . $e->getMessage(),
                'status' => 500,
                'response_data' => null
            ];
        }
    }
}
